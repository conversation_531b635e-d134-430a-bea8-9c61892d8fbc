import React, { useState, useEffect } from 'react';
import ReactECharts from 'echarts-for-react';
import './index.css';

const MarketHotspots = () => {
  const [hotStocks, setHotStocks] = useState([]);
  const [sectors, setSectors] = useState([]);
  const [loading, setLoading] = useState(true);

  // 获取热门股票数据
  const fetchHotStocks = async () => {
    try {
      const response = await fetch('/api/market/hot-stocks');
      const result = await response.json();
      if (result.success) {
        setHotStocks(result.data || []);
      }
    } catch (error) {
      console.error('获取热门股票数据失败:', error);
    }
  };

  // 获取板块数据
  const fetchSectors = async () => {
    try {
      const response = await fetch('/api/market/sectors');
      const result = await response.json();
      if (result.success) {
        setSectors(result.data || []);
      }
    } catch (error) {
      console.error('获取板块数据失败:', error);
    }
  };

  // 初始化数据
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([fetchHotStocks(), fetchSectors()]);
      setLoading(false);
    };
    loadData();
  }, []);

  // 分离涨幅榜和跌幅榜
  const topGainers = hotStocks
    .filter(stock => stock.change_percent > 0)
    .sort((a, b) => b.change_percent - a.change_percent)
    .slice(0, 5)
    .map(stock => ({
      name: stock.name,
      code: stock.code,
      price: stock.current || stock.price,
      change: stock.change,
      changePercent: stock.change_percent
    }));

  const topLosers = hotStocks
    .filter(stock => stock.change_percent < 0)
    .sort((a, b) => a.change_percent - b.change_percent)
    .slice(0, 5)
    .map(stock => ({
      name: stock.name,
      code: stock.code,
      price: stock.current || stock.price,
      change: stock.change,
      changePercent: stock.change_percent
    }));

  // 如果没有跌幅股票，使用模拟数据
  const defaultLosers = [
    { name: '中国平安', code: '601318', price: 45.67, change: -3.45, changePercent: -7.02 },
    { name: '贵州茅台', code: '600519', price: 1678.90, change: -89.45, changePercent: -5.06 },
    { name: '泸州老窖', code: '000568', price: 189.45, change: -15.67, changePercent: -7.64 }
  ];

  const displayLosers = topLosers.length > 0 ? topLosers : defaultLosers;

  // 板块资金流向数据
  const sectorData = sectors.length > 0 ? sectors.map(sector => ({
    name: sector.name,
    flow: sector.change_percent * 10, // 模拟资金流向
    change: sector.change_percent
  })) : [
    { name: '新能源', flow: 156.78, change: 8.45 },
    { name: '半导体', flow: -89.34, change: -3.21 },
    { name: '医药生物', flow: 67.89, change: 2.34 }
  ];

  const getFlowChartOption = () => {
    return {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}亿 ({d}%)'
      },
      series: [
        {
          name: '资金流向',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '50%'],
          data: sectorData.map(item => ({
            value: Math.abs(item.flow),
            name: item.name,
            itemStyle: {
              color: item.flow > 0 ? '#00d4aa' : '#ff4757'
            }
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            color: '#ffffff',
            fontSize: 12
          }
        }
      ]
    };
  };

  return (
    <div className="market-hotspots">
      <div className="section-header">
        <h3>
          <i className="fas fa-fire"></i>
          市场热点
        </h3>
        <div 
          className="refresh-btn"
          onClick={() => {
            // 刷新数据逻辑
            alert('数据已刷新');
          }}
        >
          <i className="fas fa-sync-alt"></i>
        </div>
      </div>

      <div className="hotspots-content">
        <div className="gainers-losers">
          <div className="gainers">
            <h4 className="text-up">
              <i className="fas fa-arrow-up"></i>
              涨幅榜
            </h4>
            <div className="stocks-list">
              {topGainers.map((stock, index) => (
                <div key={index} className="stock-item">
                  <div className="stock-rank">{index + 1}</div>
                  <div className="stock-info">
                    <div className="stock-name">{stock.name}</div>
                    <div className="stock-code">{stock.code}</div>
                  </div>
                  <div className="stock-data">
                    <div className="stock-price">¥{stock.price}</div>
                    <div className="stock-change text-up">
                      +{stock.changePercent.toFixed(2)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="losers">
            <h4 className="text-down">
              <i className="fas fa-arrow-down"></i>
              跌幅榜
            </h4>
            <div className="stocks-list">
              {topLosers.map((stock, index) => (
                <div key={index} className="stock-item">
                  <div className="stock-rank">{index + 1}</div>
                  <div className="stock-info">
                    <div className="stock-name">{stock.name}</div>
                    <div className="stock-code">{stock.code}</div>
                  </div>
                  <div className="stock-data">
                    <div className="stock-price">¥{stock.price}</div>
                    <div className="stock-change text-down">
                      {stock.changePercent.toFixed(2)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="sector-flow">
          <h4>
            <i className="fas fa-chart-pie"></i>
            板块资金流向
          </h4>
          <div className="flow-chart">
            <ReactECharts 
              option={getFlowChartOption()} 
              style={{ height: '200px' }}
              theme="dark"
            />
          </div>
          <div className="flow-list">
            {sectorData.map((sector, index) => (
              <div key={index} className="flow-item">
                <span className="sector-name">{sector.name}</span>
                <span className={`flow-value ${sector.flow > 0 ? 'text-up' : 'text-down'}`}>
                  {sector.flow > 0 ? '+' : ''}{sector.flow.toFixed(2)}亿
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MarketHotspots;
